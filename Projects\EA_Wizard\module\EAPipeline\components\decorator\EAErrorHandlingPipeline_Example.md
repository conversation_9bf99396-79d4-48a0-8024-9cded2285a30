# EAErrorHandlingPipeline 使用示例

## 概述

EAErrorHandlingPipeline 是一個裝飾者模式實現的錯誤處理流水線，它繼承自 EAPipelineBase，為現有的流水線添加錯誤處理功能。

## 主要功能

1. **錯誤監控**：在流水線執行過程中監控錯誤
2. **錯誤處理**：使用 EAErrorHandler 處理捕獲的錯誤
3. **異常捕獲**：捕獲執行過程中的異常
4. **結果驗證**：檢查執行結果並處理失敗情況

## 使用方法

### 基本使用

```mql4
#include "Projects\EA_Wizard\module\EAPipeline\components\decorator\EAErrorHandlingPipeline.mqh"

// 創建一個基礎流水線
Pipeline* basePipeline = new SomePipeline("基礎流水線");

// 使用 EAErrorHandlingPipeline 裝飾基礎流水線
EAErrorHandlingPipeline* errorHandlingPipeline = new EAErrorHandlingPipeline(
    basePipeline,                    // 被裝飾的流水線
    "EAErrorHandlingPipeline",       // 類型名稱
    NULL                             // 錯誤處理器（NULL 表示使用默認單例）
);

// 執行流水線
errorHandlingPipeline.Execute();

// 獲取執行結果
PipelineResult* result = errorHandlingPipeline.GetResult();
if(result != NULL)
{
    if(result.IsSuccess())
    {
        Print("流水線執行成功: ", result.GetMessage());
    }
    else
    {
        Print("流水線執行失敗: ", result.GetMessage());
    }
}
```

### 與其他裝飾者組合使用

```mql4
// 創建基礎流水線
Pipeline* basePipeline = new SomePipeline("基礎流水線");

// 先添加日誌裝飾者
EALogPipeline* logPipeline = new EALogPipeline(
    basePipeline,
    "EALogPipeline"
);

// 再添加錯誤處理裝飾者
EAErrorHandlingPipeline* errorHandlingPipeline = new EAErrorHandlingPipeline(
    logPipeline,
    "EAErrorHandlingPipeline"
);

// 執行組合裝飾者流水線
errorHandlingPipeline.Execute();
```

### 使用自定義錯誤處理器

```mql4
// 獲取自定義錯誤處理器
EAErrorHandler* customErrorHandler = EAErrorHandler::GetInstance();

// 創建帶有自定義錯誤處理器的流水線
EAErrorHandlingPipeline* errorHandlingPipeline = new EAErrorHandlingPipeline(
    basePipeline,
    "EAErrorHandlingPipeline",
    customErrorHandler
);
```

## 構造函數

### 1. 基於 Pipeline 的構造函數

```mql4
EAErrorHandlingPipeline(
    Pipeline* pipeline,                    // 被裝飾的流水線
    string type = "EAErrorHandlingPipeline", // 類型名稱
    EAErrorHandler* error_handler = NULL   // 錯誤處理器
)
```

### 2. 基於 EAPipelineBase 的構造函數

```mql4
EAErrorHandlingPipeline(
    EAPipelineBase* pipeline,              // 被裝飾的流水線基類
    string type = "EAErrorHandlingPipeline", // 類型名稱
    EAErrorHandler* error_handler = NULL   // 錯誤處理器
)
```

## 主要方法

### Execute()
執行流水線並處理可能的錯誤：
- 檢查流水線是否已執行
- 驗證被裝飾的流水線不為空
- 捕獲執行過程中的異常
- 檢查執行結果並處理失敗情況

### GetResult()
獲取執行結果並處理錯誤：
- 獲取被裝飾流水線的結果
- 檢查結果是否成功
- 如果失敗，使用錯誤處理器處理錯誤

### Restore()
重置流水線狀態並處理可能的異常：
- 捕獲重置過程中的異常
- 使用錯誤處理器記錄異常

### GetErrorHandler()
獲取當前使用的錯誤處理器實例。

## 錯誤處理機制

1. **空流水線檢查**：如果被裝飾的流水線為空，記錄錯誤並返回
2. **執行異常捕獲**：使用 try-catch 捕獲執行過程中的異常
3. **結果驗證**：檢查執行結果，如果失敗則記錄錯誤
4. **重置異常處理**：捕獲重置過程中的異常

## 注意事項

1. **錯誤處理器**：如果不提供自定義錯誤處理器，將使用 EAErrorHandler 的單例實例
2. **記憶體管理**：析構函數不會刪除錯誤處理器，因為它通常是單例
3. **裝飾者模式**：可以與其他裝飾者（如 EALogPipeline）組合使用
4. **異常安全**：所有可能拋出異常的操作都被適當的 try-catch 包圍

## 與 EAErrorHandlingCompoundPipeline 的區別

- **EAErrorHandlingPipeline**：裝飾 EAPipelineBase，適用於單個流水線
- **EAErrorHandlingCompoundPipeline**：裝飾 EACompoundPipeline，適用於複合流水線

選擇使用哪個取決於您要裝飾的流水線類型。
